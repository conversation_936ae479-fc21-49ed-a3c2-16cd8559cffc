{"version": "2.0.0", "tasks": [{"label": "Build and Run C++ (MSVC Global)", "type": "shell", "command": "cmd", "args": ["/c", "\"C:\\Program Files (x86)\\Microsoft Visual Studio\\2022\\BuildTools\\VC\\Auxiliary\\Build\\vcvars64.bat\" && cl /EHsc \"${file}\" && \"${fileBasenameNoExtension}.exe\""], "group": {"kind": "build", "isDefault": true}, "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": "$msCompile"}, {"label": "Build C++ (MSVC)", "type": "shell", "command": "cmd", "args": ["/c", "\"C:\\Program Files (x86)\\Microsoft Visual Studio\\2022\\BuildTools\\VC\\Auxiliary\\Build\\vcvars64.bat\" && cl /EHsc \"${file}\""], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": "$msCompile"}, {"label": "Run C++", "type": "shell", "command": "cmd", "args": ["/c", "\"${fileBasenameNoExtension}.exe\""], "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "dependsOn": "Build C++ (MSVC)"}]}